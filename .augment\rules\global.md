---
type: "always_apply"
---

# React Application Development Guidelines

These guidelines are intended for an AI agent assisting in the development of React applications. Adherence to these principles will ensure a clean, maintainable, and scalable codebase.

---

## 1. Project Setup & Core Architecture

### React Single Page Application (SPA)

- The project will be built as a React SPA.

### Component-Based Design

- **Single Responsibility Principle (SRP)**: Each component should have one clear purpose.
- Break down complex UIs into smaller, reusable components.

---

## 2. TypeScript Specifics

### Strict TypeScript

- Enable strict mode in `tsconfig.json`:
  ```json
  "strict": true
  ```
- This ensures early error detection and type safety.

### Prefer `type` Over `interface`

- Use `type` aliases for defining object shapes.
- Use `interface` only when features like declaration merging are needed.

### Avoid `enum`

- Do **not** use TypeScript `enum`.
- Prefer:

  - **String literal union types**:
    ```ts
    type Status = "idle" | "loading" | "succeeded" | "failed";
    ```
  - **`as const` object**:

    ```ts
    const STATUS = {
      IDLE: "idle",
      LOADING: "loading",
      SUCCEEDED: "succeeded",
      FAILED: "failed",
    } as const;

    type Status = (typeof STATUS)[keyof typeof STATUS];
    ```

### Utility Types

- Use utility types like:
  - `Pick<Type, Keys>`
  - `Omit<Type, Keys>`
- Useful for creating new types cleanly from existing ones.

---

## 3. Naming Conventions

| Element                       | Convention   | Example                                             |
| ----------------------------- | ------------ | --------------------------------------------------- |
| **Filenames/Folders**         | `kebab-case` | `user-profile.tsx`                                  |
| **Components**                | `PascalCase` | `UserProfile`, `PrimaryButton`                      |
| **Variables/Functions/Hooks** | `camelCase`  | `userData`, `fetchUserDetails`, `useFormValidation` |

---

## 4. Development Philosophy

### KISS (Keep It Simple, Stupid)

- Prioritize simplicity in design and implementation.
- Avoid unnecessary complexity.

### DRY (Don’t Repeat Yourself)

- Abstract and reuse code where possible.
- Avoid redundancy.

### Manageable File Sizes

- Keep files concise and focused.
- Split large files into smaller modules or components.

### Custom Hooks & Code Outsourcing

- Create **custom hooks** (e.g., `useSomething`) for reusable logic and side effects.
- Outsource:
  - Utility functions → `utils.ts`
  - Constants → `constants.ts`
  - Type definitions → `types.ts`

---

## 5. Technology Stack & Libraries

### State Management

- Use **React Context API** for shared/global state.
- Scope context providers appropriately to avoid unnecessary re-renders.

### UI Components

- Use **[shadcn/ui](https://ui.shadcn.com/)** for:
  - Accessible and customizable UI components.

### Styling

- Use **Tailwind CSS v4** for styling.

### Forms

- Use **React Hook Form** for:
  - Form state
  - Validation
  - Submission

### Dynamic Tables

- Use **TanStack Table** (formerly React Table) for complex, data-rich tables.

### API Calls / Data Fetching

- Use **React Query (TanStack Query)** for:
  - Data fetching
  - Caching
  - Syncing
  - Updating server state

---

## 6. Documentation & Best Practices

### Code Comments

- Add clear and concise comments, especially for non-obvious or complex logic.

### Component Props

- Define and type props clearly using TypeScript.

### Error Handling

- Implement robust error handling for:
  - API calls
  - Async operations

### Performance

- Optimize rendering with:
  - `React.memo`
  - `useCallback`
  - `useMemo` (only when needed)

### Accessibility (a11y)

- Follow **WAI-ARIA** guidelines.
- `shadcn/ui` components are generally accessible, but custom components must also comply.

### Documentation

- Keep documentation (README, Storybooks, etc.) **up to date** with code changes.

---
